Log started at 8/4/2025 1:06:34 PM
2025-08-04 13:06:34.458 [Information] LoggingService: Logging service initialized
2025-08-04 13:06:34.475 [Information] App: Starting integrated application initialization
2025-08-04 13:06:34.476 [Information] DependencyManager: Dependency manager initialized for x64 architecture
2025-08-04 13:06:34.478 [Information] X64LibraryResolver: X64LibraryResolver initialized for x64 process
2025-08-04 13:06:34.481 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-08-04 13:06:34.483 [Information] IntegratedStartupService: Setting up application environment
2025-08-04 13:06:34.484 [Information] IntegratedStartupService: Created directory: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Libraries
2025-08-04 13:06:34.486 [Information] IntegratedStartupService: Created directory: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Drivers\Vocom
2025-08-04 13:06:34.487 [Information] IntegratedStartupService: Created directory: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Config
2025-08-04 13:06:34.488 [Information] IntegratedStartupService: Created directory: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Backups
2025-08-04 13:06:34.489 [Information] IntegratedStartupService: Created directory: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Temp
2025-08-04 13:06:34.489 [Information] IntegratedStartupService: Application environment setup completed
2025-08-04 13:06:34.492 [Information] IntegratedStartupService: Resolving x64 architecture library compatibility
2025-08-04 13:06:34.495 [Information] X64LibraryResolver: Starting x64 library resolution
2025-08-04 13:06:34.498 [Information] X64LibraryResolver: Resolving Visual C++ runtime libraries
2025-08-04 13:06:34.509 [Information] X64LibraryResolver: Downloading Visual C++ Redistributable for msvcr140.dll
2025-08-04 13:07:59.298 [Information] X64LibraryResolver: ✓ Installed Visual C++ Redistributable for msvcr140.dll
2025-08-04 13:08:01.302 [Warning] X64LibraryResolver: ✗ Failed to resolve: msvcr140.dll
2025-08-04 13:08:01.307 [Information] X64LibraryResolver: ✓ Copied from system: msvcp140.dll
2025-08-04 13:08:01.311 [Information] X64LibraryResolver: ✓ Copied from system: vcruntime140.dll
2025-08-04 13:08:01.314 [Information] X64LibraryResolver: Analyzing APCI library compatibility
2025-08-04 13:08:01.315 [Warning] X64LibraryResolver: ✗ Missing: apci.dll
2025-08-04 13:08:01.315 [Warning] X64LibraryResolver: ✗ Missing: apcidb.dll
2025-08-04 13:08:01.316 [Warning] X64LibraryResolver: ✗ Missing: Volvo.ApciPlus.dll
2025-08-04 13:08:01.316 [Warning] X64LibraryResolver: ✗ Missing: Volvo.ApciPlusData.dll
2025-08-04 13:08:01.319 [Information] X64LibraryResolver: Architecture bridge not required
2025-08-04 13:08:01.321 [Information] X64LibraryResolver: Configuring environment for x64 compatibility
2025-08-04 13:08:01.326 [Information] X64LibraryResolver: Set environment variable: USE_PATCHED_IMPLEMENTATION = true
2025-08-04 13:08:01.327 [Information] X64LibraryResolver: Set environment variable: PHOENIX_VOCOM_ENABLED = true
2025-08-04 13:08:01.327 [Information] X64LibraryResolver: Set environment variable: VERBOSE_LOGGING = true
2025-08-04 13:08:01.328 [Information] X64LibraryResolver: Set environment variable: APCI_LIBRARY_PATH = D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Libraries
2025-08-04 13:08:01.328 [Information] X64LibraryResolver: Set environment variable: FORCE_ARCHITECTURE_BRIDGE = false
2025-08-04 13:08:01.328 [Information] X64LibraryResolver: Added libraries path to PATH: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Libraries
2025-08-04 13:08:01.329 [Information] X64LibraryResolver: Library resolution completed. Success: False
2025-08-04 13:08:01.330 [Warning] IntegratedStartupService: x64 library resolution completed with issues: 
2025-08-04 13:08:01.331 [Information] IntegratedStartupService: Resolved libraries: 2
2025-08-04 13:08:01.331 [Information] IntegratedStartupService: Missing libraries: 5
2025-08-04 13:08:01.332 [Information] IntegratedStartupService: Compatible libraries: 0
2025-08-04 13:08:01.333 [Information] IntegratedStartupService: Incompatible libraries: 0
2025-08-04 13:08:01.334 [Information] IntegratedStartupService: Environment variable set: USE_PATCHED_IMPLEMENTATION = true
2025-08-04 13:08:01.335 [Information] IntegratedStartupService: Environment variable set: PHOENIX_VOCOM_ENABLED = true
2025-08-04 13:08:01.335 [Information] IntegratedStartupService: Environment variable set: VERBOSE_LOGGING = true
2025-08-04 13:08:01.335 [Information] IntegratedStartupService: Environment variable set: APCI_LIBRARY_PATH = D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Libraries
2025-08-04 13:08:01.336 [Information] IntegratedStartupService: Environment variable set: FORCE_ARCHITECTURE_BRIDGE = false
2025-08-04 13:08:01.338 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-08-04 13:08:01.341 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling with integrated download
2025-08-04 13:08:01.342 [Information] VCRedistBundler: Created VCRedist directory: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Libraries\VCRedist
2025-08-04 13:08:01.344 [Information] VCRedistBundler: Copying pre-bundled Visual C++ Redistributable libraries
2025-08-04 13:08:01.347 [Information] VCRedistBundler: Copied pre-bundled library: msvcp140.dll
2025-08-04 13:08:01.348 [Information] VCRedistBundler: Copied pre-bundled library: vcruntime140.dll
2025-08-04 13:08:01.352 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-08-04 13:08:01.358 [Information] VCRedistBundler: Copied msvcr120.dll from C:\Windows\system32\msvcr120.dll
2025-08-04 13:08:01.362 [Information] VCRedistBundler: Copied msvcp120.dll from C:\Windows\system32\msvcp120.dll
2025-08-04 13:08:01.373 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-08-04 13:08:01.381 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-runtime-l1-1-0.dll
2025-08-04 13:08:01.390 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-heap-l1-1-0.dll
2025-08-04 13:08:01.396 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-string-l1-1-0.dll
2025-08-04 13:08:01.406 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-stdio-l1-1-0.dll
2025-08-04 13:08:01.412 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-math-l1-1-0.dll
2025-08-04 13:08:01.419 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-locale-l1-1-0.dll
2025-08-04 13:08:01.421 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-08-04 13:08:01.422 [Information] VCRedistBundler: About to call DownloadMissingVCRedistLibrariesAsync
2025-08-04 13:08:01.424 [Information] VCRedistBundler: Checking for missing VC++ libraries to download
2025-08-04 13:08:01.425 [Information] VCRedistBundler: Found 7 missing VC++ libraries: msvcr140.dll, api-ms-win-crt-runtime-l1-1-0.dll, api-ms-win-crt-heap-l1-1-0.dll, api-ms-win-crt-string-l1-1-0.dll, api-ms-win-crt-stdio-l1-1-0.dll, api-ms-win-crt-math-l1-1-0.dll, api-ms-win-crt-locale-l1-1-0.dll
2025-08-04 13:08:01.426 [Information] VCRedistBundler: Attempting to download Visual C++ Redistributable package
2025-08-04 13:08:01.429 [Information] VCRedistBundler: Download attempt 1/3 from: https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-08-04 13:09:20.133 [Information] VCRedistBundler: Download successful! File size: 25635768 bytes
2025-08-04 13:09:20.140 [Information] VCRedistBundler: Extracting VC++ package to: C:\Users\<USER>\AppData\Local\Temp\vcredist_extract_23ff82f1
2025-08-04 13:09:38.665 [Information] VCRedistBundler: Copied msvcp120.dll to application directory for immediate access
2025-08-04 13:09:38.670 [Information] VCRedistBundler: Copied msvcp140.dll to application directory for immediate access
2025-08-04 13:09:38.674 [Information] VCRedistBundler: Copied msvcr120.dll to application directory for immediate access
2025-08-04 13:09:38.676 [Information] VCRedistBundler: Copied vcruntime140.dll to application directory for immediate access
2025-08-04 13:09:38.677 [Information] VCRedistBundler: Completed DownloadMissingVCRedistLibrariesAsync
2025-08-04 13:09:38.679 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-08-04 13:09:38.680 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-08-04 13:09:38.870 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-08-04 13:09:39.197 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-08-04 13:09:39.198 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-08-04 13:09:39.272 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-08-04 13:09:39.273 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-08-04 13:09:39.274 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-08-04 13:09:39.274 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-08-04 13:09:39.274 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-stdio-l1-1-0.dll
2025-08-04 13:09:39.275 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-math-l1-1-0.dll
2025-08-04 13:09:39.275 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-locale-l1-1-0.dll
2025-08-04 13:09:39.282 [Information] VCRedistBundler: VC++ Redistributable verification: 4/11 (36.4%) required libraries found
2025-08-04 13:09:39.283 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: False
2025-08-04 13:09:39.285 [Warning] IntegratedStartupService: VC++ Redistributable bundling completed with warnings
2025-08-04 13:09:39.288 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 4 available, 7 missing
2025-08-04 13:09:39.289 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x64)), api-ms-win-crt-runtime-l1-1-0.dll (Universal CRT Runtime (x64)), api-ms-win-crt-heap-l1-1-0.dll (Universal CRT Heap (x64)), api-ms-win-crt-string-l1-1-0.dll (Universal CRT String (x64)), api-ms-win-crt-stdio-l1-1-0.dll (Universal CRT Standard I/O (x64)), api-ms-win-crt-math-l1-1-0.dll (Universal CRT Math (x64)), api-ms-win-crt-locale-l1-1-0.dll (Universal CRT Locale (x64))
2025-08-04 13:09:39.291 [Information] IntegratedStartupService: Checking if architecture bridge initialization is needed
2025-08-04 13:09:39.291 [Information] IntegratedStartupService: Current process architecture: x64
2025-08-04 13:09:39.291 [Information] IntegratedStartupService: No architecture compatibility issues detected
2025-08-04 13:09:39.293 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-08-04 13:09:39.295 [Information] LibraryExtractor: Starting library extraction process
2025-08-04 13:09:39.297 [Information] LibraryExtractor: Created directory: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Libraries\System
2025-08-04 13:09:39.298 [Information] LibraryExtractor: Created directory: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Drivers\Vocom\Backup
2025-08-04 13:09:39.300 [Information] LibraryExtractor: Copying pre-bundled libraries
2025-08-04 13:09:39.303 [Information] LibraryExtractor: Extracting embedded libraries
2025-08-04 13:09:39.305 [Information] LibraryExtractor: Copying system libraries
2025-08-04 13:09:39.311 [Information] LibraryExtractor: Copied system library: WUDFPuma.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-08-04 13:09:39.319 [Information] LibraryExtractor: Copied system library: apci.dll from C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021\apci.dll
2025-08-04 13:09:39.332 [Information] LibraryExtractor: Copied system library: Volvo.ApciPlus.dll from C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021\Volvo.ApciPlus.dll
2025-08-04 13:09:39.340 [Information] LibraryExtractor: Checking for missing libraries to download
2025-08-04 13:09:39.341 [Information] LibraryExtractor: Downloading missing library: msvcr120.dll from https://download.microsoft.com/download/2/E/6/2E61CFA4-993B-4DD4-91DA-3737CD5CD6E3/vcredist_x86.exe
2025-08-04 13:10:16.462 [Warning] LibraryExtractor: Target library msvcr120.dll not found in extracted files
2025-08-04 13:10:17.469 [Information] LibraryExtractor: Retrying download for msvcr120.dll from https://download.microsoft.com/download/2/E/6/2E61CFA4-993B-4DD4-91DA-3737CD5CD6E3/vcredist_x86.exe (attempt 2/3)
2025-08-04 13:10:52.855 [Warning] LibraryExtractor: Target library msvcr120.dll not found in extracted files
2025-08-04 13:10:53.857 [Information] LibraryExtractor: Retrying download for msvcr120.dll from https://download.microsoft.com/download/2/E/6/2E61CFA4-993B-4DD4-91DA-3737CD5CD6E3/vcredist_x86.exe (attempt 3/3)
2025-08-04 13:11:31.733 [Warning] LibraryExtractor: Target library msvcr120.dll not found in extracted files
2025-08-04 13:11:31.736 [Information] LibraryExtractor: Downloading missing library: msvcr120.dll from https://aka.ms/vs/17/release/vc_redist.x86.exe
2025-08-04 13:12:32.177 [Warning] LibraryExtractor: Target library msvcr120.dll not found in extracted files
2025-08-04 13:12:33.182 [Information] LibraryExtractor: Retrying download for msvcr120.dll from https://aka.ms/vs/17/release/vc_redist.x86.exe (attempt 2/3)
2025-08-04 13:13:27.948 [Warning] LibraryExtractor: Target library msvcr120.dll not found in extracted files
2025-08-04 13:13:28.952 [Information] LibraryExtractor: Retrying download for msvcr120.dll from https://aka.ms/vs/17/release/vc_redist.x86.exe (attempt 3/3)
2025-08-04 13:14:44.884 [Warning] LibraryExtractor: Extraction process timed out for msvcr120.dll
2025-08-04 13:14:44.886 [Warning] LibraryExtractor: Failed to download library: msvcr120.dll from all available URLs
2025-08-04 13:14:44.886 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-08-04 13:16:37.388 [Warning] LibraryExtractor: Extraction process timed out for msvcr140.dll
2025-08-04 13:16:38.389 [Information] LibraryExtractor: Retrying download for msvcr140.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe (attempt 2/3)
2025-08-04 13:18:14.665 [Warning] LibraryExtractor: Target library msvcr140.dll not found in extracted files
2025-08-04 13:18:15.670 [Information] LibraryExtractor: Retrying download for msvcr140.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe (attempt 3/3)
2025-08-04 13:20:14.510 [Warning] LibraryExtractor: Target library msvcr140.dll not found in extracted files
2025-08-04 13:20:14.516 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe
2025-08-04 13:20:55.503 [Warning] LibraryExtractor: Target library msvcr140.dll not found in extracted files
2025-08-04 13:20:56.508 [Information] LibraryExtractor: Retrying download for msvcr140.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe (attempt 2/3)
2025-08-04 13:21:36.707 [Warning] LibraryExtractor: Target library msvcr140.dll not found in extracted files
2025-08-04 13:21:37.712 [Information] LibraryExtractor: Retrying download for msvcr140.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe (attempt 3/3)
2025-08-04 13:22:16.441 [Warning] LibraryExtractor: Target library msvcr140.dll not found in extracted files
2025-08-04 13:22:16.446 [Warning] LibraryExtractor: Failed to download library: msvcr140.dll from all available URLs
2025-08-04 13:22:16.446 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-runtime-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-08-04 13:23:33.543 [Warning] LibraryExtractor: Target library api-ms-win-crt-runtime-l1-1-0.dll not found in extracted files
2025-08-04 13:23:34.551 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-runtime-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe (attempt 2/3)
2025-08-04 13:25:32.713 [Warning] LibraryExtractor: Extraction process timed out for api-ms-win-crt-runtime-l1-1-0.dll
2025-08-04 13:25:33.714 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-runtime-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe (attempt 3/3)
2025-08-04 13:27:19.140 [Warning] LibraryExtractor: Extraction process timed out for api-ms-win-crt-runtime-l1-1-0.dll
2025-08-04 13:27:19.142 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-runtime-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe
2025-08-04 13:28:08.526 [Warning] LibraryExtractor: Target library api-ms-win-crt-runtime-l1-1-0.dll not found in extracted files
2025-08-04 13:28:09.531 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-runtime-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe (attempt 2/3)
2025-08-04 13:28:50.431 [Warning] LibraryExtractor: Target library api-ms-win-crt-runtime-l1-1-0.dll not found in extracted files
2025-08-04 13:28:51.436 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-runtime-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe (attempt 3/3)
2025-08-04 13:29:31.081 [Warning] LibraryExtractor: Target library api-ms-win-crt-runtime-l1-1-0.dll not found in extracted files
2025-08-04 13:29:31.085 [Warning] LibraryExtractor: Failed to download library: api-ms-win-crt-runtime-l1-1-0.dll from all available URLs
2025-08-04 13:29:31.086 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-heap-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-08-04 13:31:22.338 [Warning] LibraryExtractor: Extraction process timed out for api-ms-win-crt-heap-l1-1-0.dll
2025-08-04 13:31:23.340 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-heap-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe (attempt 2/3)
2025-08-04 13:32:56.694 [Warning] LibraryExtractor: Target library api-ms-win-crt-heap-l1-1-0.dll not found in extracted files
2025-08-04 13:32:57.702 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-heap-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe (attempt 3/3)
2025-08-04 13:34:15.084 [Warning] LibraryExtractor: Target library api-ms-win-crt-heap-l1-1-0.dll not found in extracted files
2025-08-04 13:34:15.091 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-heap-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe
2025-08-04 13:34:55.240 [Warning] LibraryExtractor: Target library api-ms-win-crt-heap-l1-1-0.dll not found in extracted files
2025-08-04 13:34:56.245 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-heap-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe (attempt 2/3)
2025-08-04 13:35:35.490 [Warning] LibraryExtractor: Target library api-ms-win-crt-heap-l1-1-0.dll not found in extracted files
2025-08-04 13:35:36.495 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-heap-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe (attempt 3/3)
2025-08-04 13:36:17.589 [Warning] LibraryExtractor: Target library api-ms-win-crt-heap-l1-1-0.dll not found in extracted files
2025-08-04 13:36:17.593 [Warning] LibraryExtractor: Failed to download library: api-ms-win-crt-heap-l1-1-0.dll from all available URLs
2025-08-04 13:36:17.594 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-string-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-08-04 13:37:37.990 [Warning] LibraryExtractor: Target library api-ms-win-crt-string-l1-1-0.dll not found in extracted files
2025-08-04 13:37:38.999 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-string-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe (attempt 2/3)
2025-08-04 13:39:01.110 [Warning] LibraryExtractor: Target library api-ms-win-crt-string-l1-1-0.dll not found in extracted files
2025-08-04 13:39:02.119 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-string-l1-1-0.dll from https://aka.ms/vs/17/release/vc_redist.x64.exe (attempt 3/3)
2025-08-04 13:40:29.660 [Warning] LibraryExtractor: Target library api-ms-win-crt-string-l1-1-0.dll not found in extracted files
2025-08-04 13:40:29.667 [Information] LibraryExtractor: Downloading missing library: api-ms-win-crt-string-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe
2025-08-04 13:41:17.392 [Warning] LibraryExtractor: Target library api-ms-win-crt-string-l1-1-0.dll not found in extracted files
2025-08-04 13:41:18.396 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-string-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe (attempt 2/3)
2025-08-04 13:41:57.563 [Warning] LibraryExtractor: Target library api-ms-win-crt-string-l1-1-0.dll not found in extracted files
2025-08-04 13:41:58.568 [Information] LibraryExtractor: Retrying download for api-ms-win-crt-string-l1-1-0.dll from https://download.microsoft.com/download/9/3/F/93FCF1E7-E6A4-478B-96E7-D4B285925B00/vc_redist.x64.exe (attempt 3/3)
2025-08-04 13:42:38.876 [Warning] LibraryExtractor: Target library api-ms-win-crt-string-l1-1-0.dll not found in extracted files
2025-08-04 13:42:38.881 [Warning] LibraryExtractor: Failed to download library: api-ms-win-crt-string-l1-1-0.dll from all available URLs
2025-08-04 13:42:38.881 [Warning] LibraryExtractor: Reached maximum download attempts (5), skipping remaining libraries to prevent hanging
2025-08-04 13:42:38.881 [Information] LibraryExtractor: Completed download phase with 5 attempts
2025-08-04 13:42:38.884 [Information] LibraryExtractor: Verifying library extraction
2025-08-04 13:42:38.884 [Information] LibraryExtractor: ✓ Verified: WUDFPuma.dll
2025-08-04 13:42:38.885 [Information] LibraryExtractor: ✓ Verified: apci.dll
2025-08-04 13:42:38.885 [Information] LibraryExtractor: ✓ Verified: Volvo.ApciPlus.dll
2025-08-04 13:42:38.885 [Information] LibraryExtractor: Library verification: 3/3 (100.0%) required libraries found
2025-08-04 13:42:38.886 [Information] LibraryExtractor: Library extraction completed. Success: True
2025-08-04 13:42:38.888 [Information] IntegratedStartupService: Library extraction status: 3 available, 0 missing
2025-08-04 13:42:38.890 [Information] IntegratedStartupService: Initializing dependency manager
2025-08-04 13:42:38.965 [Information] DependencyManager: Initializing dependency manager
2025-08-04 13:42:38.966 [Information] DependencyManager: Setting up library search paths
2025-08-04 13:42:38.968 [Information] DependencyManager: Added library path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Libraries
2025-08-04 13:42:38.968 [Information] DependencyManager: Added driver path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Drivers\Vocom
2025-08-04 13:42:38.969 [Information] DependencyManager: Added application path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64
2025-08-04 13:42:38.969 [Information] DependencyManager: Updated PATH environment variable
2025-08-04 13:42:38.971 [Information] DependencyManager: Verifying required directories
2025-08-04 13:42:38.971 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Libraries
2025-08-04 13:42:38.971 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Drivers\Vocom
2025-08-04 13:42:38.971 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Libraries\System
2025-08-04 13:42:38.972 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Config
2025-08-04 13:42:38.974 [Information] DependencyManager: Loading Visual C++ Redistributable libraries
2025-08-04 13:42:38.981 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcr120.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\msvcr120.dll (x64)
2025-08-04 13:42:39.041 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp120.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\msvcp120.dll (x64)
2025-08-04 13:42:39.044 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-08-04 13:42:39.044 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-08-04 13:42:39.098 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp140.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Libraries\msvcp140.dll (x64)
2025-08-04 13:42:39.099 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: vcruntime140.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Libraries\vcruntime140.dll (x64)
2025-08-04 13:42:39.100 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-08-04 13:42:39.100 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-08-04 13:42:39.101 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-08-04 13:42:39.102 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-08-04 13:42:39.103 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-08-04 13:42:39.103 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-08-04 13:42:39.104 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-stdio-l1-1-0.dll
2025-08-04 13:42:39.105 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-stdio-l1-1-0.dll
2025-08-04 13:42:39.105 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-math-l1-1-0.dll
2025-08-04 13:42:39.106 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-math-l1-1-0.dll
2025-08-04 13:42:39.107 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-locale-l1-1-0.dll
2025-08-04 13:42:39.107 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-locale-l1-1-0.dll
2025-08-04 13:42:39.108 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-convert-l1-1-0.dll
2025-08-04 13:42:39.108 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-convert-l1-1-0.dll
2025-08-04 13:42:39.109 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-time-l1-1-0.dll
2025-08-04 13:42:39.110 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-time-l1-1-0.dll
2025-08-04 13:42:39.111 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-filesystem-l1-1-0.dll
2025-08-04 13:42:39.111 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-filesystem-l1-1-0.dll
2025-08-04 13:42:39.111 [Information] DependencyManager: VC++ Redistributable library loading: 4/14 (28.6%) libraries loaded
2025-08-04 13:42:39.111 [Warning] DependencyManager: Low VC++ Redistributable library loading success rate - some functionality may be limited
2025-08-04 13:42:39.113 [Information] DependencyManager: Loading critical Vocom libraries
2025-08-04 13:42:39.114 [Information] DependencyManager: ✓ Loaded Critical library: WUDFPuma.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Libraries\WUDFPuma.dll (x64)
2025-08-04 13:42:39.219 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Libraries\apci.dll
2025-08-04 13:42:39.220 [Warning] DependencyManager: Failed to load Critical library apci.dll: Error 193
2025-08-04 13:42:39.221 [Warning] DependencyManager: Critical library not found: apcidb.dll
2025-08-04 13:42:39.529 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Libraries\Volvo.ApciPlus.dll
2025-08-04 13:42:39.530 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlus.dll: Error 193
2025-08-04 13:42:39.531 [Warning] DependencyManager: Critical library not found: Volvo.ApciPlusData.dll
2025-08-04 13:42:39.532 [Warning] DependencyManager: Critical library not found: PhoenixGeneral.dll
2025-08-04 13:42:39.533 [Information] DependencyManager: ✓ Loaded Critical library: msvcr120.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\msvcr120.dll (x64)
2025-08-04 13:42:39.534 [Information] DependencyManager: ✓ Loaded Critical library: msvcp120.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\msvcp120.dll (x64)
2025-08-04 13:42:39.535 [Warning] DependencyManager: Critical library not found: msvcr140.dll
2025-08-04 13:42:39.536 [Information] DependencyManager: ✓ Loaded Critical library: msvcp140.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Libraries\msvcp140.dll (x64)
2025-08-04 13:42:39.536 [Information] DependencyManager: ✓ Loaded Critical library: vcruntime140.dll from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Libraries\vcruntime140.dll (x64)
2025-08-04 13:42:39.537 [Information] DependencyManager: Setting up environment variables
2025-08-04 13:42:39.537 [Information] DependencyManager: Environment variables configured
2025-08-04 13:42:39.539 [Information] DependencyManager: Verifying library loading status
2025-08-04 13:42:39.866 [Information] DependencyManager: Library loading verification: 5/11 (45.5%) critical libraries loaded
2025-08-04 13:42:39.866 [Warning] DependencyManager: Low library loading success rate - some functionality may be limited
2025-08-04 13:42:39.867 [Information] DependencyManager: Dependency manager initialized successfully
2025-08-04 13:42:39.870 [Information] IntegratedStartupService: Dependency status: 7 found, 4 missing
2025-08-04 13:42:39.872 [Information] IntegratedStartupService: Setting up Vocom-specific environment
2025-08-04 13:42:39.935 [Information] IntegratedStartupService: Created Vocom configuration: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Drivers\Vocom\config.json
2025-08-04 13:42:39.936 [Information] IntegratedStartupService: Vocom environment setup completed
2025-08-04 13:42:39.938 [Information] IntegratedStartupService: Verifying system readiness
2025-08-04 13:42:39.939 [Information] IntegratedStartupService: System readiness verification passed
2025-08-04 13:42:39.940 [Information] IntegratedStartupService: === Integrated Application Initialization Complete ===
2025-08-04 13:42:39.942 [Information] IntegratedStartupService: === System Status Summary ===
2025-08-04 13:42:39.942 [Information] IntegratedStartupService: Application Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64
2025-08-04 13:42:39.942 [Information] IntegratedStartupService: Libraries Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Libraries
2025-08-04 13:42:39.943 [Information] IntegratedStartupService: Drivers Path: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Drivers\Vocom
2025-08-04 13:42:39.943 [Information] IntegratedStartupService: Environment Variable USE_PATCHED_IMPLEMENTATION: true
2025-08-04 13:42:39.943 [Information] IntegratedStartupService: Environment Variable PHOENIX_VOCOM_ENABLED: true
2025-08-04 13:42:39.944 [Information] IntegratedStartupService: Environment Variable VERBOSE_LOGGING: true
2025-08-04 13:42:39.945 [Information] IntegratedStartupService: Environment Variable APCI_LIBRARY_PATH: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Libraries
2025-08-04 13:42:39.945 [Information] IntegratedStartupService: === End System Status Summary ===
2025-08-04 13:42:39.945 [Information] App: Integrated startup completed successfully
2025-08-04 13:42:39.949 [Information] App: System Status - Libraries: 3 available, Dependencies: 7 loaded
2025-08-04 13:42:40.163 [Information] App: Initializing application services
2025-08-04 13:42:40.165 [Information] AppConfigurationService: Initializing configuration service
2025-08-04 13:42:40.166 [Information] AppConfigurationService: Created configuration directory: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Config
2025-08-04 13:42:40.166 [Information] AppConfigurationService: Configuration file not found, creating default
2025-08-04 13:42:40.170 [Warning] AppConfigurationService: Configuration service not initialized
2025-08-04 13:42:40.170 [Information] AppConfigurationService: Default configuration created
2025-08-04 13:42:40.170 [Information] AppConfigurationService: Configuration service initialized successfully
2025-08-04 13:42:40.171 [Information] App: Configuration service initialized successfully
2025-08-04 13:42:40.172 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-08-04 13:42:40.172 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-08-04 13:42:40.172 [Information] App: Environment variable exists: True, not 'false': False
2025-08-04 13:42:40.173 [Information] App: Final useDummyImplementations value: False
2025-08-04 13:42:40.173 [Information] App: Updating config to NOT use dummy implementations
2025-08-04 13:42:40.201 [Information] AppConfigurationService: Configuration saved to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Config\app_config.json
2025-08-04 13:42:40.203 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-08-04 13:42:40.204 [Information] App: usePatchedImplementation flag is: True
2025-08-04 13:42:40.204 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-08-04 13:42:40.204 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Libraries'
2025-08-04 13:42:40.205 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-08-04 13:42:40.205 [Information] App: verboseLogging flag is: True
2025-08-04 13:42:40.208 [Information] App: Verifying real hardware requirements...
2025-08-04 13:42:40.208 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-08-04 13:42:40.209 [Information] App: ✓ Found critical library: apci.dll
2025-08-04 13:42:40.209 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-08-04 13:42:40.209 [Warning] App: ✗ Missing critical library: Volvo.ApciPlusData.dll at D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Libraries\Volvo.ApciPlusData.dll
2025-08-04 13:42:40.210 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-08-04 13:42:40.210 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-08-04 13:42:40.210 [Information] App: ✓ Found Vocom driver config: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Drivers\Vocom\config.json
2025-08-04 13:42:40.211 [Warning] App: ⚠ Some real hardware requirements are missing - application may fall back to dummy mode
2025-08-04 13:42:40.222 [Information] App: *** RESOLVING RUNTIME DEPENDENCIES ***
2025-08-04 13:42:40.225 [Information] VCRuntimeInstaller: Starting Visual C++ runtime dependency installation
2025-08-04 13:42:40.225 [Information] VCRuntimeInstaller: Process architecture: x64
2025-08-04 13:42:40.230 [Information] VCRuntimeInstaller: Found 1 missing Visual C++ runtime libraries
2025-08-04 13:42:40.233 [Information] VCRuntimeInstaller: Installing Visual C++ redistributable: vc_redist.x64.exe
2025-08-04 13:42:40.234 [Information] VCRuntimeInstaller: Attempting to download from: https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-08-04 13:44:00.106 [Information] VCRuntimeInstaller: Downloaded redistributable to: C:\Users\<USER>\AppData\Local\Temp\vcredist_ec0f4a5c-ff33-4101-bf71-116729a244c9.exe
2025-08-04 13:44:00.108 [Information] VCRuntimeInstaller: Running Visual C++ redistributable installer: C:\Users\<USER>\AppData\Local\Temp\vcredist_ec0f4a5c-ff33-4101-bf71-116729a244c9.exe
2025-08-04 13:44:05.043 [Information] VCRuntimeInstaller: Visual C++ redistributable installer exit code: 0
2025-08-04 13:44:05.049 [Information] VCRuntimeInstaller: Successfully installed Visual C++ redistributable from https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-08-04 13:44:05.052 [Warning] VCRuntimeInstaller: Visual C++ runtime installation partially successful. 1 libraries still missing
2025-08-04 13:44:05.053 [Warning] App: VCRuntimeInstaller had issues: 
2025-08-04 13:44:05.055 [Information] EnhancedRuntimeInstaller: Starting enhanced runtime dependency installation
2025-08-04 13:44:05.056 [Information] EnhancedRuntimeInstaller: Process architecture: x64
2025-08-04 13:44:05.060 [Warning] EnhancedRuntimeInstaller: Missing runtime library: Microsoft Visual C++ 2015-2022 Runtime (msvcr140.dll)
2025-08-04 13:44:05.060 [Information] EnhancedRuntimeInstaller: Found 1 missing runtime libraries
2025-08-04 13:44:05.062 [Information] EnhancedRuntimeInstaller: Downloading Visual C++ Redistributable (x64)
2025-08-04 13:45:40.539 [Information] EnhancedRuntimeInstaller: Installing Visual C++ Redistributable (this may take a few minutes)
2025-08-04 13:45:47.903 [Information] EnhancedRuntimeInstaller: Visual C++ Redistributable installed successfully
2025-08-04 13:45:47.915 [Information] EnhancedRuntimeInstaller: Checking Universal CRT availability
2025-08-04 13:45:47.915 [Information] EnhancedRuntimeInstaller: Windows 10+ detected - Universal CRT should be available via Windows Update
2025-08-04 13:45:47.918 [Warning] EnhancedRuntimeInstaller: Missing runtime library: Microsoft Visual C++ 2015-2022 Runtime (msvcr140.dll)
2025-08-04 13:45:47.919 [Information] EnhancedRuntimeInstaller: Runtime installation completed: 0/1 libraries resolved
2025-08-04 13:45:47.920 [Warning] App: Enhanced runtime installer also had issues, trying final fallback resolver
2025-08-04 13:45:47.922 [Information] RuntimeDependencyResolver: Checking Visual C++ runtime dependencies
2025-08-04 13:45:47.923 [Information] RuntimeDependencyResolver: Process architecture: x64
2025-08-04 13:45:47.925 [Information] RuntimeDependencyResolver: Attempting to resolve missing library: msvcr140.dll
2025-08-04 13:45:47.932 [Information] RuntimeDependencyResolver: Attempting to download msvcr140.dll from Microsoft redistributable
2025-08-04 13:47:12.491 [Warning] RuntimeDependencyResolver: ✗ Failed to resolve: msvcr140.dll
2025-08-04 13:47:12.492 [Information] RuntimeDependencyResolver: ✓ Already available: msvcp140.dll
2025-08-04 13:47:12.492 [Information] RuntimeDependencyResolver: ✓ Already available: vcruntime140.dll
2025-08-04 13:47:12.493 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-runtime-l1-1-0.dll
2025-08-04 13:47:12.493 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-heap-l1-1-0.dll
2025-08-04 13:47:12.493 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-string-l1-1-0.dll
2025-08-04 13:47:12.495 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-stdio-l1-1-0.dll
2025-08-04 13:47:12.495 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-math-l1-1-0.dll
2025-08-04 13:47:12.495 [Information] RuntimeDependencyResolver: ✓ Already available: api-ms-win-crt-locale-l1-1-0.dll
2025-08-04 13:47:12.496 [Information] RuntimeDependencyResolver: Runtime dependency resolution: 8/9 libraries available
2025-08-04 13:47:12.498 [Information] RuntimeDependencyResolver: === Visual C++ Runtime Installation Guidance ===
2025-08-04 13:47:12.498 [Information] RuntimeDependencyResolver: If you continue to experience issues with missing Visual C++ runtime libraries:
2025-08-04 13:47:12.498 [Information] RuntimeDependencyResolver: 1. Download and install Microsoft Visual C++ 2015-2022 Redistributable (x64)
2025-08-04 13:47:12.499 [Information] RuntimeDependencyResolver: 2. Download link: https://aka.ms/vs/17/release/vc_redist.x64.exe
2025-08-04 13:47:12.499 [Information] RuntimeDependencyResolver: 3. Restart the application after installation
2025-08-04 13:47:12.499 [Information] RuntimeDependencyResolver: 4. Ensure Windows is up to date
2025-08-04 13:47:12.499 [Information] RuntimeDependencyResolver: === End Installation Guidance ===
2025-08-04 13:47:12.500 [Information] App: *** CREATING ARCHITECTURE-AWARE VOCOM SERVICE ***
2025-08-04 13:47:12.503 [Information] ArchitectureAwareVocomServiceFactory: === Architecture-Aware Vocom Service Factory ===
2025-08-04 13:47:12.503 [Information] ArchitectureAwareVocomServiceFactory: Current process architecture: x64
2025-08-04 13:47:12.505 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: apci.dll
2025-08-04 13:47:12.506 [Warning] ArchitectureAwareVocomServiceFactory: ✗ Architecture mismatch: Volvo.ApciPlus.dll
2025-08-04 13:47:12.508 [Information] ArchitectureAwareVocomServiceFactory: ✓ Compatible library: WUDFPuma.dll
2025-08-04 13:47:12.508 [Warning] ArchitectureAwareVocomServiceFactory: Found 2 incompatible libraries
2025-08-04 13:47:12.510 [Information] ArchitectureAwareVocomServiceFactory: Library compatibility check: ArchitectureMismatch
2025-08-04 13:47:12.511 [Information] ArchitectureAwareVocomServiceFactory: Architecture mismatch detected - using bridge service immediately
2025-08-04 13:47:12.511 [Warning] ArchitectureAwareVocomServiceFactory: Architecture bridge not found at: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Bridge\VolvoFlashWR.VocomBridge.exe
2025-08-04 13:47:12.512 [Warning] ArchitectureAwareVocomServiceFactory: Architecture bridge not available - attempting compatibility workarounds
2025-08-04 13:47:12.513 [Information] ArchitectureAwareVocomServiceFactory: Creating compatibility Vocom service for architecture mismatch handling
2025-08-04 13:47:12.516 [Information] CompatibilityVocomService: Initializing Compatibility Vocom Service
2025-08-04 13:47:12.516 [Information] CompatibilityVocomService: This service uses WMI-based detection to avoid architecture mismatch issues
2025-08-04 13:47:12.618 [Information] CompatibilityVocomService: Compatibility Vocom Service initialized successfully
2025-08-04 13:47:12.618 [Information] ArchitectureAwareVocomServiceFactory: Compatibility Vocom service initialized successfully
2025-08-04 13:47:12.619 [Information] ArchitectureAwareVocomServiceFactory: Compatibility service created successfully
2025-08-04 13:47:12.619 [Information] App: Architecture-aware Vocom service created successfully
2025-08-04 13:47:12.619 [Information] CompatibilityVocomService: Initializing Compatibility Vocom Service
2025-08-04 13:47:12.619 [Information] CompatibilityVocomService: This service uses WMI-based detection to avoid architecture mismatch issues
2025-08-04 13:47:12.631 [Information] CompatibilityVocomService: Compatibility Vocom Service initialized successfully
2025-08-04 13:47:12.631 [Information] App: Architecture-aware Vocom service initialized successfully
2025-08-04 13:47:12.631 [Information] App: Using VolvoFlashWR.Communication.Vocom.ArchitectureBridge.ArchitectureAwareVocomServiceFactory Vocom service factory
2025-08-04 13:47:12.632 [Information] App: Checking if PTT application is running before creating Vocom service
2025-08-04 13:47:12.698 [Information] CompatibilityVocomService: Scanning for Vocom devices using WMI-based detection
2025-08-04 13:47:13.206 [Information] CompatibilityVocomService: Compatibility scan found 0 Vocom devices
2025-08-04 13:47:13.207 [Warning] App: No Vocom devices found, continuing without a connected device
2025-08-04 13:47:13.212 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-08-04 13:47:13.218 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-08-04 13:47:13.219 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-08-04 13:47:13.226 [Information] ECUCommunicationService: Initializing ECU communication service
2025-08-04 13:47:13.228 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-08-04 13:47:13.229 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-08-04 13:47:13.232 [Information] ECUCommunicationService: === ECU SERVICE DEVICE CHECK DEBUG ===
2025-08-04 13:47:13.232 [Information] ECUCommunicationService: _vocomService.CurrentDevice != null: False
2025-08-04 13:47:13.232 [Information] ECUCommunicationService: === END ECU SERVICE DEVICE CHECK DEBUG ===
2025-08-04 13:47:13.233 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-08-04 13:47:13.233 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-08-04 13:47:13.235 [Information] CompatibilityVocomService: Connecting to first available device (compatibility mode)
2025-08-04 13:47:13.235 [Information] CompatibilityVocomService: Scanning for Vocom devices using WMI-based detection
2025-08-04 13:47:13.621 [Information] CompatibilityVocomService: Compatibility scan found 0 Vocom devices
2025-08-04 13:47:13.622 [Warning] CompatibilityVocomService: No devices found to connect to
2025-08-04 13:47:13.623 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-08-04 13:47:13.625 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 1/3
2025-08-04 13:47:14.628 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 2/3)
2025-08-04 13:47:14.628 [Information] ECUCommunicationService: Initializing ECU communication service
2025-08-04 13:47:14.630 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-08-04 13:47:14.631 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-08-04 13:47:14.631 [Information] ECUCommunicationService: === ECU SERVICE DEVICE CHECK DEBUG ===
2025-08-04 13:47:14.632 [Information] ECUCommunicationService: _vocomService.CurrentDevice != null: False
2025-08-04 13:47:14.632 [Information] ECUCommunicationService: === END ECU SERVICE DEVICE CHECK DEBUG ===
2025-08-04 13:47:14.632 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-08-04 13:47:14.632 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-08-04 13:47:14.633 [Information] CompatibilityVocomService: Connecting to first available device (compatibility mode)
2025-08-04 13:47:14.633 [Information] CompatibilityVocomService: Scanning for Vocom devices using WMI-based detection
2025-08-04 13:47:15.048 [Information] CompatibilityVocomService: Compatibility scan found 0 Vocom devices
2025-08-04 13:47:15.048 [Warning] CompatibilityVocomService: No devices found to connect to
2025-08-04 13:47:15.049 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-08-04 13:47:15.049 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 2/3
2025-08-04 13:47:17.049 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 3/3)
2025-08-04 13:47:17.050 [Information] ECUCommunicationService: Initializing ECU communication service
2025-08-04 13:47:17.050 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-08-04 13:47:17.050 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-08-04 13:47:17.051 [Information] ECUCommunicationService: === ECU SERVICE DEVICE CHECK DEBUG ===
2025-08-04 13:47:17.051 [Information] ECUCommunicationService: _vocomService.CurrentDevice != null: False
2025-08-04 13:47:17.051 [Information] ECUCommunicationService: === END ECU SERVICE DEVICE CHECK DEBUG ===
2025-08-04 13:47:17.051 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-08-04 13:47:17.052 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-08-04 13:47:17.052 [Information] CompatibilityVocomService: Connecting to first available device (compatibility mode)
2025-08-04 13:47:17.052 [Information] CompatibilityVocomService: Scanning for Vocom devices using WMI-based detection
2025-08-04 13:47:17.425 [Information] CompatibilityVocomService: Compatibility scan found 0 Vocom devices
2025-08-04 13:47:17.425 [Warning] CompatibilityVocomService: No devices found to connect to
2025-08-04 13:47:17.425 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-08-04 13:47:17.425 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 3/3
2025-08-04 13:47:20.427 [Error] ECUCommunicationServiceFactory: Failed to initialize ECU communication service after 3 attempts
2025-08-04 13:47:20.428 [Warning] ECUCommunicationServiceFactory: Creating DummyECUCommunicationService as fallback
2025-08-04 13:47:20.429 [Information] DummyECUCommunicationService: DummyECUCommunicationService created
2025-08-04 13:47:20.431 [Information] DummyECUCommunicationService: Initializing DummyECUCommunicationService
2025-08-04 13:47:20.932 [Information] DummyECUCommunicationService: DummyECUCommunicationService initialized successfully
2025-08-04 13:47:20.932 [Information] ECUCommunicationServiceFactory: DummyECUCommunicationService initialized successfully as fallback
2025-08-04 13:47:20.937 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-08-04 13:47:20.939 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-08-04 13:47:20.942 [Information] BackupService: Initializing backup service
2025-08-04 13:47:20.942 [Information] BackupService: Backup service initialized successfully
2025-08-04 13:47:20.943 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-08-04 13:47:20.943 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-08-04 13:47:20.946 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-08-04 13:47:20.978 [Information] BackupService: Compressing backup data
2025-08-04 13:47:20.986 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (450 bytes)
2025-08-04 13:47:20.988 [Information] BackupServiceFactory: Created template for category: Production
2025-08-04 13:47:20.989 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-08-04 13:47:20.990 [Information] BackupService: Compressing backup data
2025-08-04 13:47:20.992 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (451 bytes)
2025-08-04 13:47:20.992 [Information] BackupServiceFactory: Created template for category: Development
2025-08-04 13:47:20.992 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-08-04 13:47:20.994 [Information] BackupService: Compressing backup data
2025-08-04 13:47:20.995 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (448 bytes)
2025-08-04 13:47:20.997 [Information] BackupServiceFactory: Created template for category: Testing
2025-08-04 13:47:20.997 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-08-04 13:47:20.999 [Information] BackupService: Compressing backup data
2025-08-04 13:47:21.000 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (452 bytes)
2025-08-04 13:47:21.000 [Information] BackupServiceFactory: Created template for category: Archived
2025-08-04 13:47:21.000 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-08-04 13:47:21.001 [Information] BackupService: Compressing backup data
2025-08-04 13:47:21.002 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (446 bytes)
2025-08-04 13:47:21.002 [Information] BackupServiceFactory: Created template for category: Critical
2025-08-04 13:47:21.003 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-08-04 13:47:21.003 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-08-04 13:47:21.004 [Information] BackupService: Compressing backup data
2025-08-04 13:47:21.005 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (514 bytes)
2025-08-04 13:47:21.005 [Information] BackupServiceFactory: Created template with predefined tags
2025-08-04 13:47:21.006 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-08-04 13:47:21.010 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-08-04 13:47:21.013 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-08-04 13:47:21.014 [Information] BackupSchedulerService: Created schedules directory: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Schedules
2025-08-04 13:47:21.018 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-08-04 13:47:21.019 [Information] BackupSchedulerService: Schedules file not found: D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Schedules\backup_schedules.json
2025-08-04 13:47:21.019 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-08-04 13:47:21.020 [Information] BackupSchedulerService: Starting backup scheduler
2025-08-04 13:47:21.020 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-08-04 13:47:21.021 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-08-04 13:47:21.023 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-08-04 13:47:21.023 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-08-04 13:47:21.028 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-08-04 13:47:21.029 [Information] App: Flash operation monitor service initialized successfully
2025-08-04 13:47:21.044 [Information] LicensingService: Initializing licensing service
2025-08-04 13:47:21.135 [Information] LicensingService: License information saved successfully
2025-08-04 13:47:21.139 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-08-04 13:47:21.141 [Information] App: Licensing service initialized successfully
2025-08-04 13:47:21.142 [Information] App: License status: Trial
2025-08-04 13:47:21.145 [Information] App: Trial period: 30 days remaining
2025-08-04 13:47:21.146 [Information] BackupSchedulerService: Getting all backup schedules
2025-08-04 13:47:21.147 [Information] App: Creating default backup schedules
2025-08-04 13:47:21.151 [Information] DummyECUCommunicationService: Scanning for ECUs
2025-08-04 13:47:22.185 [Information] DummyECUCommunicationService: Found 3 ECUs
2025-08-04 13:47:22.205 [Information] BackupSchedulerService: Creating backup schedule for ECU EMS
2025-08-04 13:47:22.208 [Information] BackupSchedulerService: Saving backup schedules to disk
2025-08-04 13:47:22.375 [Information] BackupSchedulerService: Saved 1 backup schedules to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Schedules\backup_schedules.json
2025-08-04 13:47:22.376 [Information] BackupSchedulerService: Backup schedule created for ECU EMS
2025-08-04 13:47:22.376 [Information] App: Created daily backup schedule for EMS
2025-08-04 13:47:22.376 [Information] BackupSchedulerService: Creating backup schedule for ECU EMS
2025-08-04 13:47:22.377 [Information] BackupSchedulerService: Saving backup schedules to disk
2025-08-04 13:47:22.379 [Information] BackupSchedulerService: Saved 2 backup schedules to D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Schedules\backup_schedules.json
2025-08-04 13:47:22.379 [Information] BackupSchedulerService: Backup schedule created for ECU EMS
2025-08-04 13:47:22.379 [Information] App: Created weekly backup schedule for EMS
2025-08-04 13:47:22.908 [Information] CompatibilityVocomService: Initializing Compatibility Vocom Service
2025-08-04 13:47:22.909 [Information] CompatibilityVocomService: This service uses WMI-based detection to avoid architecture mismatch issues
2025-08-04 13:47:23.346 [Information] CompatibilityVocomService: Compatibility Vocom Service initialized successfully
2025-08-04 13:47:23.493 [Information] DummyECUCommunicationService: Initializing DummyECUCommunicationService
2025-08-04 13:47:24.097 [Information] DummyECUCommunicationService: DummyECUCommunicationService initialized successfully
2025-08-04 13:47:24.168 [Information] BackupService: Initializing backup service
2025-08-04 13:47:24.168 [Information] BackupService: Backup service initialized successfully
2025-08-04 13:47:24.254 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-08-04 13:47:24.254 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-08-04 13:47:24.331 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\Development\S.A.H.VolvoFlashWR\VolvoFlashWR.UI\bin\Release\net8.0-windows\win-x64\Schedules\backup_schedules.json
2025-08-04 13:47:24.332 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-08-04 13:47:24.396 [Information] BackupService: Getting predefined backup categories
2025-08-04 13:47:24.519 [Information] MainViewModel: Services initialized successfully
2025-08-04 13:47:24.523 [Information] MainViewModel: Scanning for Vocom devices
2025-08-04 13:47:24.524 [Information] CompatibilityVocomService: Scanning for Vocom devices using WMI-based detection
2025-08-04 13:47:25.035 [Information] CompatibilityVocomService: Compatibility scan found 0 Vocom devices
2025-08-04 13:47:25.035 [Information] MainViewModel: Found 0 Vocom device(s)
