using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.Communication.Vocom.ArchitectureBridge
{
    /// <summary>
    /// Vocom service that uses the architecture bridge to communicate with x86 APCI libraries
    /// This provides a seamless interface while solving the architecture mismatch issue
    /// </summary>
    public class BridgedVocomService : IVocomService
    {
        private readonly ILoggingService _logger;
        private readonly VocomArchitectureBridge _bridge;
        private bool _isInitialized;
        private bool _isConnected;
        private VocomDevice? _connectedDevice;
        private VocomDevice? _lastConnectedDevice; // Store last connected device for reconnection
        private static readonly object _initLock = new object();
        private VocomConnectionSettings? _connectionSettings;
        private DateTime _lastConnectionStateCheck = DateTime.MinValue;
        private readonly TimeSpan _connectionStateCheckInterval = TimeSpan.FromSeconds(5);

        // Error handling and retry configuration
        private const int MaxRetryAttempts = 3;
        private const int RetryDelayMs = 1000;
        private int _consecutiveFailures = 0;
        private DateTime _lastFailureTime = DateTime.MinValue;

        // Connection recovery configuration
        private bool _autoRecoveryEnabled = true;
        private readonly TimeSpan _recoveryCheckInterval = TimeSpan.FromSeconds(30);
        private DateTime _lastRecoveryCheck = DateTime.MinValue;
        private bool _isRecovering = false;

        public bool IsConnected => _isConnected;
        public VocomDevice? ConnectedDevice => _connectedDevice;
        public VocomDevice? CurrentDevice => _connectedDevice;
        public ConnectionSettings ConnectionSettings { get; private set; } = new ConnectionSettings();

        /// <summary>
        /// Gets the last time connection state was validated
        /// </summary>
        public DateTime LastConnectionStateCheck => _lastConnectionStateCheck;

        /// <summary>
        /// Gets or sets whether automatic connection recovery is enabled
        /// </summary>
        public bool AutoRecoveryEnabled
        {
            get => _autoRecoveryEnabled;
            set => _autoRecoveryEnabled = value;
        }

        /// <summary>
        /// Gets whether the service is currently performing recovery operations
        /// </summary>
        public bool IsRecovering => _isRecovering;

        // Events
        public event EventHandler<VocomDevice>? VocomConnected;
        public event EventHandler<VocomDevice>? VocomDisconnected;
        public event EventHandler<string>? VocomError;

        public BridgedVocomService(ILoggingService logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _bridge = new VocomArchitectureBridge(_logger);
        }

        /// <summary>
        /// Initializes the bridged Vocom service
        /// </summary>
        public async Task<bool> InitializeAsync()
        {
            try
            {
                _logger.LogInformation("Initializing Bridged Vocom Service", "BridgedVocomService");

                if (await _bridge.InitializeAsync())
                {
                    _isInitialized = true;
                    _logger.LogInformation("Bridged Vocom Service initialized successfully", "BridgedVocomService");
                    return true;
                }

                _logger.LogWarning("Bridge initialization failed - service will operate in limited mode", "BridgedVocomService");
                // Don't return false, allow service to continue with limited functionality
                _isInitialized = true; // Mark as initialized but with limited functionality
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Bridge initialization error: {ex.Message} - continuing with limited functionality", "BridgedVocomService");
                _isInitialized = true; // Mark as initialized but with limited functionality
                return true;
            }
        }

        /// <summary>
        /// Scans for available Vocom devices
        /// </summary>
        public async Task<List<VocomDevice>> ScanForDevicesAsync()
        {
            if (!_isInitialized)
            {
                _logger.LogWarning("Service not initialized, cannot scan for devices", "BridgedVocomService");
                return new List<VocomDevice>();
            }

            try
            {
                _logger.LogInformation("Scanning for Vocom devices through bridge", "BridgedVocomService");

                // Check if bridge is available
                if (!_bridge.IsAvailable)
                {
                    _logger.LogWarning("Bridge not available - returning simulated device for testing", "BridgedVocomService");
                    var simulatedDevice = new VocomDevice
                    {
                        Id = "SIMULATED_VOCOM_001",
                        Name = "Simulated Vocom Device",
                        ConnectionType = VocomConnectionType.USB,
                        ConnectionStatus = VocomConnectionStatus.Disconnected
                    };
                    return new List<VocomDevice> { simulatedDevice };
                }

                _logger.LogInformation("=== About to call _bridge.DetectDevicesAsync() ===", "BridgedVocomService");

                // Use retry mechanism for device detection
                var devices = await ExecuteWithRetryAsync<IEnumerable<VocomDevice>>(
                    async () => await _bridge.DetectDevicesAsync(),
                    "DetectDevices",
                    new List<VocomDevice>());

                _logger.LogInformation("=== Returned from _bridge.DetectDevicesAsync() ===", "BridgedVocomService");

                var deviceList = devices.ToList();
                _logger.LogInformation($"Found {deviceList.Count} Vocom devices", "BridgedVocomService");

                return deviceList;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception during device scan: {ex.Message}", "BridgedVocomService");
                return new List<VocomDevice>();
            }
        }

        /// <summary>
        /// Connects to a specific Vocom device
        /// </summary>
        public async Task<bool> ConnectToDeviceAsync(VocomDevice device)
        {
            if (!_isInitialized)
            {
                _logger.LogWarning("Service not initialized, cannot connect to device", "BridgedVocomService");
                return false;
            }

            if (device == null)
            {
                _logger.LogError("Device parameter is null", "BridgedVocomService");
                return false;
            }

            // Validate current connection state before attempting new connection
            await ValidateConnectionStateAsync();

            // Check service health before attempting connection
            if (!IsServiceHealthy())
            {
                _logger.LogWarning("Service is not healthy, connection attempt may fail", "BridgedVocomService");
            }

            try
            {
                _logger.LogInformation($"Connecting to Vocom device {device.Id} through bridge", "BridgedVocomService");

                // Check if bridge is available
                if (!_bridge.IsAvailable)
                {
                    _logger.LogWarning("Bridge not available - attempting to initialize bridge for real hardware connection", "BridgedVocomService");

                    // Try to initialize the bridge for real hardware
                    try
                    {
                        await _bridge.InitializeAsync();
                        if (_bridge.IsAvailable)
                        {
                            _logger.LogInformation("Bridge successfully initialized, proceeding with real connection", "BridgedVocomService");
                        }
                        else
                        {
                            _logger.LogWarning("Bridge initialization failed - using compatibility mode for testing", "BridgedVocomService");
                            _isConnected = true;
                            _connectedDevice = device;
                            _lastConnectedDevice = device; // Store for reconnection
                            // Update device connection status
                            device.ConnectionStatus = VocomConnectionStatus.Connected;
                            device.LastConnectionTime = DateTime.Now;
                            VocomConnected?.Invoke(this, device);
                            _logger.LogInformation($"Compatibility connection to device {device.Id} (bridge not available)", "BridgedVocomService");
                            return true;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError($"Failed to initialize bridge: {ex.Message}", "BridgedVocomService");
                        _logger.LogWarning("Using compatibility mode for testing", "BridgedVocomService");
                        _isConnected = true;
                        _connectedDevice = device;
                        _lastConnectedDevice = device; // Store for reconnection
                        // Update device connection status
                        device.ConnectionStatus = VocomConnectionStatus.Connected;
                        device.LastConnectionTime = DateTime.Now;
                        VocomConnected?.Invoke(this, device);
                        _logger.LogInformation($"Compatibility connection to device {device.Id} (bridge initialization failed)", "BridgedVocomService");
                        return true;
                    }
                }

                // Use retry mechanism for bridge connection
                var connectionResult = await ExecuteWithRetryAsync(
                    async () => await _bridge.ConnectToDeviceAsync(device.Id),
                    $"ConnectToDevice({device.Id})",
                    false);

                if (connectionResult)
                {
                    _isConnected = true;
                    _connectedDevice = device;
                    _lastConnectedDevice = device; // Store for reconnection
                    // Update device connection status
                    device.ConnectionStatus = VocomConnectionStatus.Connected;
                    device.LastConnectionTime = DateTime.Now;

                    // Debug logging
                    _logger.LogInformation($"=== DEVICE CONNECTION DEBUG ===", "BridgedVocomService");
                    _logger.LogInformation($"Device ID: {device.Id}", "BridgedVocomService");
                    _logger.LogInformation($"Device Name: {device.Name}", "BridgedVocomService");
                    _logger.LogInformation($"Device ConnectionStatus: {device.ConnectionStatus}", "BridgedVocomService");
                    _logger.LogInformation($"_isConnected: {_isConnected}", "BridgedVocomService");
                    _logger.LogInformation($"CurrentDevice != null: {CurrentDevice != null}", "BridgedVocomService");
                    if (CurrentDevice != null)
                    {
                        _logger.LogInformation($"CurrentDevice.ConnectionStatus: {CurrentDevice.ConnectionStatus}", "BridgedVocomService");
                    }
                    _logger.LogInformation($"=== END DEVICE CONNECTION DEBUG ===", "BridgedVocomService");

                    VocomConnected?.Invoke(this, device);
                    _logger.LogInformation($"Successfully connected to device {device.Id}", "BridgedVocomService");
                    return true;
                }

                _logger.LogError($"Failed to connect to device {device.Id}", "BridgedVocomService");
                VocomError?.Invoke(this, $"Failed to connect to device {device.Id}");
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception during device connection: {ex.Message}", "BridgedVocomService");
                VocomError?.Invoke(this, ex.Message);
                return false;
            }
        }

        /// <summary>
        /// Connects to a specific Vocom device (alternative signature)
        /// </summary>
        public async Task<bool> ConnectAsync(VocomDevice device)
        {
            return await ConnectToDeviceAsync(device);
        }

        /// <summary>
        /// Disconnects from the current device
        /// </summary>
        public async Task<bool> DisconnectAsync()
        {
            if (!_isConnected)
            {
                _logger.LogInformation("No device connected, disconnect not needed", "BridgedVocomService");
                return true;
            }

            try
            {
                _logger.LogInformation("Disconnecting from Vocom device through bridge", "BridgedVocomService");

                var disconnectedDevice = _connectedDevice;
                bool bridgeDisconnectSuccess = true;

                // Send disconnect command through bridge if available
                if (_bridge.IsAvailable && disconnectedDevice != null)
                {
                    _logger.LogInformation($"Sending disconnect command to bridge for device {disconnectedDevice.Id}", "BridgedVocomService");

                    // Use retry mechanism for bridge disconnect
                    bridgeDisconnectSuccess = await ExecuteWithRetryAsync(
                        async () => await _bridge.DisconnectFromDeviceAsync(),
                        $"DisconnectFromDevice({disconnectedDevice.Id})",
                        false);

                    if (!bridgeDisconnectSuccess)
                    {
                        _logger.LogWarning("Bridge disconnect command failed after retries, proceeding with local cleanup", "BridgedVocomService");
                    }
                    else
                    {
                        _logger.LogInformation("Bridge disconnect command completed successfully", "BridgedVocomService");
                    }
                }
                else
                {
                    _logger.LogInformation("Bridge not available or no device connected, performing local cleanup only", "BridgedVocomService");
                }

                // Always perform local cleanup regardless of bridge disconnect result
                _isConnected = false;
                _connectedDevice = null;

                if (disconnectedDevice != null)
                {
                    // Update device connection status
                    disconnectedDevice.ConnectionStatus = VocomConnectionStatus.Disconnected;
                    disconnectedDevice.LastConnectionTime = DateTime.Now;
                    VocomDisconnected?.Invoke(this, disconnectedDevice);
                    _logger.LogInformation($"Device {disconnectedDevice.Id} marked as disconnected", "BridgedVocomService");
                }

                _logger.LogInformation("Successfully disconnected from Vocom device", "BridgedVocomService");
                return bridgeDisconnectSuccess; // Return true only if both bridge and local cleanup succeeded
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception during disconnect: {ex.Message}", "BridgedVocomService");

                // Ensure cleanup even if exception occurred
                _isConnected = false;
                _connectedDevice = null;

                VocomError?.Invoke(this, ex.Message);
                return false;
            }
        }

        /// <summary>
        /// Connects to the first available device
        /// </summary>
        public async Task<bool> ConnectToFirstAvailableDeviceAsync()
        {
            try
            {
                var devices = await ScanForDevicesAsync();
                if (devices.Count > 0)
                {
                    return await ConnectToDeviceAsync(devices[0]);
                }

                _logger.LogWarning("No devices available for connection", "BridgedVocomService");
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception connecting to first available device: {ex.Message}", "BridgedVocomService");
                VocomError?.Invoke(this, ex.Message);
                return false;
            }
        }

        /// <summary>
        /// Reconnects to the current device
        /// </summary>
        public async Task<bool> ReconnectAsync()
        {
            // Validate connection state before attempting reconnection
            await ValidateConnectionStateAsync();

            // Try to use the currently connected device first, then fall back to last connected device
            var deviceToReconnect = _connectedDevice ?? _lastConnectedDevice;

            if (deviceToReconnect != null)
            {
                _logger.LogInformation($"Attempting to reconnect to device {deviceToReconnect.Id}", "BridgedVocomService");

                // Disconnect first if currently connected
                if (_isConnected)
                {
                    await DisconnectAsync();
                }

                return await ConnectToDeviceAsync(deviceToReconnect);
            }

            _logger.LogWarning("No previous device to reconnect to", "BridgedVocomService");
            return false;
        }

        /// <summary>
        /// Validates and synchronizes connection state between local service and bridge
        /// </summary>
        public async Task<bool> ValidateConnectionStateAsync()
        {
            try
            {
                // Only check periodically to avoid excessive bridge communication
                if (DateTime.Now - _lastConnectionStateCheck < _connectionStateCheckInterval)
                {
                    return true;
                }

                _lastConnectionStateCheck = DateTime.Now;

                // Check if auto-recovery should be performed
                await CheckAndPerformAutoRecoveryAsync();

                if (!_bridge.IsAvailable)
                {
                    _logger.LogDebug("Bridge not available, skipping connection state validation", "BridgedVocomService");
                    return true;
                }

                // Get current bridge state
                var bridgeDevices = await _bridge.DetectDevicesAsync();
                var connectedBridgeDevice = bridgeDevices?.FirstOrDefault(d => d.ConnectionStatus == VocomConnectionStatus.Connected);

                // Synchronize local state with bridge state
                if (connectedBridgeDevice != null && !_isConnected)
                {
                    _logger.LogWarning("Bridge reports connected device but local state shows disconnected - synchronizing", "BridgedVocomService");

                    // Update local state to match bridge
                    _isConnected = true;
                    _connectedDevice = new VocomDevice
                    {
                        Id = connectedBridgeDevice.Id,
                        Name = connectedBridgeDevice.Name,
                        ConnectionType = connectedBridgeDevice.ConnectionType,
                        ConnectionStatus = VocomConnectionStatus.Connected,
                        LastConnectionTime = DateTime.Now
                    };
                    _lastConnectedDevice = _connectedDevice;

                    _logger.LogInformation($"Synchronized local state: connected to device {_connectedDevice.Id}", "BridgedVocomService");
                    VocomConnected?.Invoke(this, _connectedDevice);
                }
                else if (connectedBridgeDevice == null && _isConnected)
                {
                    // Be more careful about disconnecting - only if we're sure the bridge is working
                    if (_bridge.IsAvailable && _bridge.IsBridgeProcessHealthy())
                    {
                        _logger.LogWarning("Local state shows connected but bridge reports no connected device - synchronizing", "BridgedVocomService");

                        // Update local state to match bridge
                        var previousDevice = _connectedDevice;
                        _isConnected = false;
                        _connectedDevice = null;

                        if (previousDevice != null)
                        {
                            previousDevice.ConnectionStatus = VocomConnectionStatus.Disconnected;
                            _logger.LogInformation($"Synchronized local state: disconnected from device {previousDevice.Id}", "BridgedVocomService");
                            VocomDisconnected?.Invoke(this, previousDevice);
                        }
                    }
                    else
                    {
                        _logger.LogDebug("Bridge not healthy, skipping disconnection synchronization", "BridgedVocomService");
                    }
                }
                else if (connectedBridgeDevice != null && _isConnected && _connectedDevice != null)
                {
                    // Both show connected - verify it's the same device
                    if (_connectedDevice.Id != connectedBridgeDevice.Id)
                    {
                        _logger.LogWarning($"Device mismatch: local={_connectedDevice.Id}, bridge={connectedBridgeDevice.Id} - updating local state", "BridgedVocomService");

                        // Disconnect from old device locally
                        var oldDevice = _connectedDevice;
                        oldDevice.ConnectionStatus = VocomConnectionStatus.Disconnected;
                        VocomDisconnected?.Invoke(this, oldDevice);

                        // Connect to new device locally
                        _connectedDevice = new VocomDevice
                        {
                            Id = connectedBridgeDevice.Id,
                            Name = connectedBridgeDevice.Name,
                            ConnectionType = connectedBridgeDevice.ConnectionType,
                            ConnectionStatus = VocomConnectionStatus.Connected,
                            LastConnectionTime = DateTime.Now
                        };
                        _lastConnectedDevice = _connectedDevice;
                        VocomConnected?.Invoke(this, _connectedDevice);
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception during connection state validation: {ex.Message}", "BridgedVocomService");
                return false;
            }
        }

        /// <summary>
        /// Executes an operation with retry logic for bridge communication failures
        /// </summary>
        private async Task<T> ExecuteWithRetryAsync<T>(Func<Task<T>> operation, string operationName, T defaultValue = default(T))
        {
            Exception lastException = null;

            for (int attempt = 1; attempt <= MaxRetryAttempts; attempt++)
            {
                try
                {
                    var result = await operation();

                    // Reset failure count on success
                    _consecutiveFailures = 0;

                    return result;
                }
                catch (Exception ex)
                {
                    lastException = ex;
                    _consecutiveFailures++;
                    _lastFailureTime = DateTime.Now;

                    _logger.LogWarning($"Attempt {attempt}/{MaxRetryAttempts} failed for {operationName}: {ex.Message}", "BridgedVocomService");

                    if (attempt < MaxRetryAttempts)
                    {
                        var delay = RetryDelayMs * attempt; // Exponential backoff
                        _logger.LogInformation($"Retrying {operationName} in {delay}ms", "BridgedVocomService");
                        await Task.Delay(delay);
                    }
                }
            }

            _logger.LogError($"All {MaxRetryAttempts} attempts failed for {operationName}. Last error: {lastException?.Message}", "BridgedVocomService");
            VocomError?.Invoke(this, $"Operation {operationName} failed after {MaxRetryAttempts} attempts: {lastException?.Message}");

            return defaultValue;
        }

        /// <summary>
        /// Checks if the service is in a healthy state for operations
        /// </summary>
        private bool IsServiceHealthy()
        {
            // Consider service unhealthy if there have been too many consecutive failures recently
            if (_consecutiveFailures >= MaxRetryAttempts &&
                DateTime.Now - _lastFailureTime < TimeSpan.FromMinutes(5))
            {
                _logger.LogWarning($"Service considered unhealthy: {_consecutiveFailures} consecutive failures", "BridgedVocomService");
                return false;
            }

            return true;
        }

        /// <summary>
        /// Attempts to recover from bridge communication failures
        /// </summary>
        public async Task<bool> AttemptRecoveryAsync()
        {
            if (_isRecovering)
            {
                _logger.LogInformation("Recovery already in progress, skipping", "BridgedVocomService");
                return false;
            }

            _isRecovering = true;
            _logger.LogInformation("Starting connection recovery process", "BridgedVocomService");

            try
            {
                // Step 1: Check if bridge is still available and healthy
                if (!_bridge.IsAvailable || !_bridge.IsBridgeProcessHealthy())
                {
                    _logger.LogWarning("Bridge is not available or unhealthy, attempting to restart", "BridgedVocomService");

                    // Try to restart the bridge process
                    var bridgeRestarted = await _bridge.RestartBridgeProcessAsync();
                    if (!bridgeRestarted)
                    {
                        _logger.LogError("Failed to restart bridge process during recovery", "BridgedVocomService");
                        return false;
                    }

                    _logger.LogInformation("Bridge process restarted successfully", "BridgedVocomService");
                }

                // Step 2: Validate connection state
                await ValidateConnectionStateAsync();

                // Step 3: If we had a connected device but lost connection, try to reconnect
                if (_lastConnectedDevice != null && !_isConnected)
                {
                    _logger.LogInformation($"Attempting to recover connection to device {_lastConnectedDevice.Id}", "BridgedVocomService");

                    var reconnected = await ConnectToDeviceAsync(_lastConnectedDevice);
                    if (reconnected)
                    {
                        _logger.LogInformation($"Successfully recovered connection to device {_lastConnectedDevice.Id}", "BridgedVocomService");
                        _consecutiveFailures = 0; // Reset failure count on successful recovery
                        return true;
                    }
                    else
                    {
                        _logger.LogWarning($"Failed to recover connection to device {_lastConnectedDevice.Id}", "BridgedVocomService");
                    }
                }

                // Step 4: If no specific device to reconnect to, try to find and connect to any available device
                if (!_isConnected)
                {
                    _logger.LogInformation("No previous device to recover, attempting to connect to any available device", "BridgedVocomService");

                    var connected = await ConnectToFirstAvailableDeviceAsync();
                    if (connected)
                    {
                        _logger.LogInformation("Successfully connected to an available device during recovery", "BridgedVocomService");
                        _consecutiveFailures = 0;
                        return true;
                    }
                }

                _logger.LogWarning("Recovery attempt completed but no connection established", "BridgedVocomService");
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception during recovery attempt: {ex.Message}", "BridgedVocomService");
                return false;
            }
            finally
            {
                _isRecovering = false;
                _lastRecoveryCheck = DateTime.Now;
            }
        }

        /// <summary>
        /// Checks if automatic recovery should be triggered and performs it if needed
        /// </summary>
        public async Task<bool> CheckAndPerformAutoRecoveryAsync()
        {
            if (!_autoRecoveryEnabled)
            {
                return false;
            }

            // Only check periodically to avoid excessive recovery attempts
            if (DateTime.Now - _lastRecoveryCheck < _recoveryCheckInterval)
            {
                return false;
            }

            // Trigger recovery if service is unhealthy or if we should be connected but aren't
            bool shouldRecover = !IsServiceHealthy() ||
                                (_lastConnectedDevice != null && !_isConnected);

            if (shouldRecover)
            {
                _logger.LogInformation("Auto-recovery conditions met, attempting recovery", "BridgedVocomService");
                return await AttemptRecoveryAsync();
            }

            _lastRecoveryCheck = DateTime.Now;
            return false;
        }

        /// <summary>
        /// Sends data to the connected device
        /// </summary>
        public async Task<bool> SendDataAsync(byte[] data)
        {
            if (!_isConnected)
            {
                _logger.LogError("No device connected, cannot send data", "BridgedVocomService");
                return false;
            }

            try
            {
                _logger.LogDebug($"Sending {data.Length} bytes through bridge", "BridgedVocomService");
                
                // Implementation would send data through the bridge
                // This is a placeholder for the actual implementation
                
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception during data send: {ex.Message}", "BridgedVocomService");
                return false;
            }
        }

        /// <summary>
        /// Receives data from the connected device
        /// </summary>
        public async Task<byte[]?> ReceiveDataAsync(int timeoutMs = 5000)
        {
            if (!_isConnected)
            {
                _logger.LogError("No device connected, cannot receive data", "BridgedVocomService");
                return null;
            }

            try
            {
                _logger.LogDebug("Receiving data through bridge", "BridgedVocomService");
                
                // Implementation would receive data through the bridge
                // This is a placeholder for the actual implementation
                
                return Array.Empty<byte>();
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception during data receive: {ex.Message}", "BridgedVocomService");
                return null;
            }
        }

        /// <summary>
        /// Gets the status of the Vocom service
        /// </summary>
        public VocomServiceStatus GetStatus()
        {
            return new VocomServiceStatus
            {
                IsInitialized = _isInitialized,
                IsConnected = _isConnected,
                ConnectedDevice = _connectedDevice,
                LastError = null // Could be enhanced to track last error
            };
        }

        /// <summary>
        /// Checks if PTT application is running
        /// </summary>
        public bool IsPttRunning()
        {
            try
            {
                // Check for PTT processes
                var processes = System.Diagnostics.Process.GetProcessesByName("PTT");
                return processes.Length > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception checking PTT status: {ex.Message}", "BridgedVocomService");
                return false;
            }
        }

        /// <summary>
        /// Checks if PTT application is running (async version)
        /// </summary>
        public async Task<bool> IsPTTRunningAsync()
        {
            return await Task.FromResult(IsPttRunning());
        }

        /// <summary>
        /// Disconnects PTT application
        /// </summary>
        public async Task<bool> DisconnectPTTAsync()
        {
            return await TerminatePttAsync();
        }

        /// <summary>
        /// Terminates PTT application if running
        /// </summary>
        public async Task<bool> TerminatePttAsync()
        {
            try
            {
                var processes = System.Diagnostics.Process.GetProcessesByName("PTT");
                if (processes.Length == 0)
                {
                    _logger.LogInformation("No PTT processes found", "BridgedVocomService");
                    return true;
                }

                foreach (var process in processes)
                {
                    try
                    {
                        _logger.LogInformation($"Terminating PTT process {process.Id}", "BridgedVocomService");
                        process.Kill();
                        await process.WaitForExitAsync();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning($"Failed to terminate PTT process {process.Id}: {ex.Message}", "BridgedVocomService");
                    }
                    finally
                    {
                        process.Dispose();
                    }
                }

                _logger.LogInformation("PTT termination completed", "BridgedVocomService");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception during PTT termination: {ex.Message}", "BridgedVocomService");
                return false;
            }
        }

        /// <summary>
        /// Checks if Bluetooth is enabled
        /// </summary>
        public async Task<bool> IsBluetoothEnabledAsync()
        {
            try
            {
                // Placeholder implementation - would check Bluetooth status through bridge
                _logger.LogInformation("Checking Bluetooth status through bridge", "BridgedVocomService");
                return await Task.FromResult(false); // Default to false for now
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception checking Bluetooth status: {ex.Message}", "BridgedVocomService");
                return false;
            }
        }

        /// <summary>
        /// Enables Bluetooth
        /// </summary>
        public async Task<bool> EnableBluetoothAsync()
        {
            try
            {
                // Placeholder implementation - would enable Bluetooth through bridge
                _logger.LogInformation("Enabling Bluetooth through bridge", "BridgedVocomService");
                return await Task.FromResult(false); // Default to false for now
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception enabling Bluetooth: {ex.Message}", "BridgedVocomService");
                return false;
            }
        }

        /// <summary>
        /// Checks if WiFi is available
        /// </summary>
        public async Task<bool> IsWiFiAvailableAsync()
        {
            try
            {
                // Placeholder implementation - would check WiFi status through bridge
                _logger.LogInformation("Checking WiFi availability through bridge", "BridgedVocomService");
                return await Task.FromResult(false); // Default to false for now
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception checking WiFi availability: {ex.Message}", "BridgedVocomService");
                return false;
            }
        }

        /// <summary>
        /// Checks if device is MC9S12XEP100 ECU
        /// </summary>
        public async Task<bool> IsMC9S12XEP100ECUAsync(string deviceId)
        {
            try
            {
                // Placeholder implementation - would check ECU type through bridge
                _logger.LogInformation($"Checking if device {deviceId} is MC9S12XEP100 ECU through bridge", "BridgedVocomService");
                return await Task.FromResult(true); // Default to true for testing
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception checking ECU type: {ex.Message}", "BridgedVocomService");
                return false;
            }
        }

        /// <summary>
        /// Updates connection settings
        /// </summary>
        public void UpdateConnectionSettings(ConnectionSettings settings)
        {
            try
            {
                _logger.LogInformation("Updating connection settings", "BridgedVocomService");
                // Convert and store settings
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception updating connection settings: {ex.Message}", "BridgedVocomService");
                VocomError?.Invoke(this, ex.Message);
            }
        }

        /// <summary>
        /// Sets Vocom connection settings
        /// </summary>
        public async Task<bool> SetConnectionSettings(VocomConnectionSettings settings)
        {
            try
            {
                _connectionSettings = settings;
                _logger.LogInformation("Vocom connection settings updated", "BridgedVocomService");
                return await Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception setting Vocom connection settings: {ex.Message}", "BridgedVocomService");
                VocomError?.Invoke(this, ex.Message);
                return await Task.FromResult(false);
            }
        }

        /// <summary>
        /// Gets the current device
        /// </summary>
        public async Task<VocomDevice?> GetCurrentDeviceAsync()
        {
            return await Task.FromResult(_connectedDevice);
        }

        /// <summary>
        /// Reads MC9S12XEP100 registers
        /// </summary>
        public async Task<Dictionary<string, object>> ReadMC9S12XEP100RegistersAsync(string ecuId)
        {
            try
            {
                _logger.LogInformation($"Reading MC9S12XEP100 registers for ECU {ecuId} through bridge", "BridgedVocomService");
                // Placeholder implementation - would read registers through bridge
                return await Task.FromResult(new Dictionary<string, object>());
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception reading MC9S12XEP100 registers: {ex.Message}", "BridgedVocomService");
                VocomError?.Invoke(this, ex.Message);
                return new Dictionary<string, object>();
            }
        }

        /// <summary>
        /// Sends CAN frame data
        /// </summary>
        public async Task<byte[]> SendCANFrameAsync(VocomDevice device, uint canId, byte[] data, int timeoutMs = 5000)
        {
            try
            {
                _logger.LogDebug($"Sending CAN frame {canId:X} with {data.Length} bytes through bridge", "BridgedVocomService");
                // Placeholder implementation - would send CAN frame through bridge
                return await Task.FromResult(new byte[0]);
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception sending CAN frame: {ex.Message}", "BridgedVocomService");
                VocomError?.Invoke(this, ex.Message);
                return new byte[0];
            }
        }

        /// <summary>
        /// Sends and receives data
        /// </summary>
        public async Task<byte[]> SendAndReceiveDataAsync(byte[] data, int responseTimeout = 1000)
        {
            try
            {
                _logger.LogDebug($"Sending and receiving {data.Length} bytes through bridge", "BridgedVocomService");
                // Placeholder implementation - would send/receive data through bridge
                return await Task.FromResult(new byte[0]);
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception sending/receiving data: {ex.Message}", "BridgedVocomService");
                VocomError?.Invoke(this, ex.Message);
                return new byte[0];
            }
        }

        /// <summary>
        /// Sends SPI data
        /// </summary>
        public async Task<byte[]> SendSPIDataAsync(VocomDevice device, byte[] data, int responseTimeout = 1000)
        {
            try
            {
                _logger.LogDebug($"Sending SPI data with {data.Length} bytes through bridge", "BridgedVocomService");
                // Placeholder implementation - would send SPI data through bridge
                return await Task.FromResult(new byte[0]);
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception sending SPI data: {ex.Message}", "BridgedVocomService");
                VocomError?.Invoke(this, ex.Message);
                return new byte[0];
            }
        }

        /// <summary>
        /// Sends SCI data
        /// </summary>
        public async Task<byte[]> SendSCIDataAsync(VocomDevice device, byte[] data, int responseTimeout = 1000)
        {
            try
            {
                _logger.LogDebug($"Sending SCI data with {data.Length} bytes through bridge", "BridgedVocomService");
                // Placeholder implementation - would send SCI data through bridge
                return await Task.FromResult(new byte[0]);
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception sending SCI data: {ex.Message}", "BridgedVocomService");
                VocomError?.Invoke(this, ex.Message);
                return new byte[0];
            }
        }

        /// <summary>
        /// Sends IIC data
        /// </summary>
        public async Task<byte[]> SendIICDataAsync(VocomDevice device, byte address, byte[] data, int responseTimeout = 1000)
        {
            try
            {
                _logger.LogDebug($"Sending IIC data to address {address:X2} with {data.Length} bytes through bridge", "BridgedVocomService");
                // Placeholder implementation - would send IIC data through bridge
                return await Task.FromResult(new byte[0]);
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception sending IIC data: {ex.Message}", "BridgedVocomService");
                VocomError?.Invoke(this, ex.Message);
                return new byte[0];
            }
        }

        public void Dispose()
        {
            try
            {
                if (_isConnected)
                {
                    DisconnectAsync().Wait(5000);
                }

                _bridge?.Dispose();
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception during disposal: {ex.Message}", "BridgedVocomService");
            }
        }
    }

    /// <summary>
    /// Status information for the Vocom service
    /// </summary>
    public class VocomServiceStatus
    {
        public bool IsInitialized { get; set; }
        public bool IsConnected { get; set; }
        public VocomDevice? ConnectedDevice { get; set; }
        public string? LastError { get; set; }
    }
}
